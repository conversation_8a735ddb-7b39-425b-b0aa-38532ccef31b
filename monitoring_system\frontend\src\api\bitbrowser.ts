/**
 * BitBrowser集成API服务
 */

import { request } from '@/utils/request'

// 类型定义
export interface BrowserInfo {
  id: string
  name: string
  seq: number
  remark: string
  is_open: boolean
  pid?: number
  network_status: string
  proxy_status?: any
}

export interface SystemStatus {
  bitbrowser_connected: boolean
  total_browsers: number
  available_browsers: number
  system_ready: boolean
  network_status: {
    total: number
    normal: number
    no_proxy: number
    skip: number
    error: number
    unknown: number
  }
  browsers: BrowserInfo[]
}

export interface CollectionRequest {
  browser_ids: string[]
  max_concurrent?: number
}

export interface CollectionResult {
  success: boolean
  browser_id: string
  browser_name: string
  error?: string
  notes_count: number
  data?: {
    total_count: number
    collected_at: string
    page_url: string
    notes: Array<{
      index: number
      title: string
      publish_time: string
      views: string
      comments: string
      likes: string
      collections: string
      shares: string
    }>
  }
}

export interface CollectionStatus {
  active_collections: number
  collections: Record<string, {
    status: string
    duration: number
    start_time: number
  }>
}

// 窗口扫描结果
export interface WindowScanResult {
  total_scanned: number
  available_windows: number
  active_windows: number
  windows: BrowserInfo[]
  scan_time: string
}

/**
 * BitBrowser API服务类
 */
export class BitBrowserAPI {
  private baseURL = '/api/v1/bitbrowser'

  /**
   * 获取系统状态
   */
  async getSystemStatus(): Promise<SystemStatus> {
    const response = await request.get(`${this.baseURL}/system/status`)
    return response.data
  }

  /**
   * 采集单个浏览器数据
   */
  async collectSingleBrowser(browserId: string): Promise<CollectionResult> {
    const response = await request.post(`${this.baseURL}/collect/single/${browserId}`)
    return response.data
  }

  /**
   * 批量采集多个浏览器数据
   */
  async collectMultipleBrowsers(request: CollectionRequest): Promise<CollectionResult[]> {
    const response = await request.post(`${this.baseURL}/collect/multiple`, request)
    return response.data
  }

  /**
   * 获取采集状态
   */
  async getCollectionStatus(): Promise<CollectionStatus> {
    const response = await request.get(`${this.baseURL}/collection/status`)
    return response.data
  }

  /**
   * 取消采集
   */
  async cancelCollection(browserId: string): Promise<{ message: string }> {
    const response = await request.delete(`${this.baseURL}/collect/cancel/${browserId}`)
    return response.data
  }

  /**
   * 获取可用浏览器列表
   */
  async getAvailableBrowsers(): Promise<{
    total_available: number
    browsers: BrowserInfo[]
  }> {
    const response = await request.get(`${this.baseURL}/browsers/available`)
    return response.data
  }

  /**
   * 扫描BitBrowser窗口
   */
  async scanWindows(): Promise<WindowScanResult> {
    const response = await request.get(`${this.baseURL}/scan/windows`)
    return response.data
  }
}

// 导出单例实例
export const bitbrowserAPI = new BitBrowserAPI()

// 导出默认实例
export default bitbrowserAPI
