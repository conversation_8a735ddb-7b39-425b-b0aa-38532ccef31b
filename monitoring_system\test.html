<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .loading {
            background-color: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 API连接测试</h1>
        
        <div class="test-section">
            <h3>测试1: 简单连接测试</h3>
            <button onclick="testSimpleConnection()">测试连接</button>
            <div id="simple-result"></div>
        </div>

        <div class="test-section">
            <h3>测试2: 系统状态API</h3>
            <button onclick="testSystemStatus()">获取系统状态</button>
            <div id="status-result"></div>
        </div>

        <div class="test-section">
            <h3>测试3: 采集状态API</h3>
            <button onclick="testCollectionStatus()">获取采集状态</button>
            <div id="collection-result"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://127.0.0.1:8001/api/v1';

        // 添加详细的错误日志
        window.addEventListener('error', function(e) {
            console.error('页面错误:', e.error);
        });

        async function testAPI(url, resultElementId, description) {
            const resultElement = document.getElementById(resultElementId);
            resultElement.innerHTML = `<div class="loading">⏳ ${description}中...</div>`;
            
            try {
                const response = await fetch(url);
                const data = await response.json();
                
                if (response.ok) {
                    resultElement.innerHTML = `
                        <div class="success">
                            ✅ ${description}成功！
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    resultElement.innerHTML = `
                        <div class="error">
                            ❌ ${description}失败！状态码: ${response.status}
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                resultElement.innerHTML = `
                    <div class="error">
                        ❌ ${description}失败！
                        <pre>错误: ${error.message}</pre>
                    </div>
                `;
            }
        }

        function testSimpleConnection() {
            testAPI(`${API_BASE}/bitbrowser/test`, 'simple-result', '简单连接测试');
        }

        function testSystemStatus() {
            testAPI(`${API_BASE}/bitbrowser/system/status`, 'status-result', '系统状态获取');
        }

        function testCollectionStatus() {
            testAPI(`${API_BASE}/bitbrowser/collection/status`, 'collection-result', '采集状态获取');
        }

        // 页面加载时自动测试
        window.onload = function() {
            console.log('🚀 页面加载完成，开始自动测试...');
            setTimeout(() => {
                testSimpleConnection();
                setTimeout(() => testSystemStatus(), 1000);
                setTimeout(() => testCollectionStatus(), 2000);
            }, 500);
        };
    </script>
</body>
</html>
