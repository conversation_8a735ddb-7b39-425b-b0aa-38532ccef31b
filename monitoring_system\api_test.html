<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API连接测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #555;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 API连接测试</h1>
        
        <div class="test-section">
            <h3>测试1: 简单连接测试</h3>
            <button onclick="testConnection()">测试连接</button>
            <div id="test1-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>测试2: 系统状态API</h3>
            <button onclick="testSystemStatus()">获取系统状态</button>
            <div id="test2-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>测试3: 采集状态API</h3>
            <button onclick="testCollectionStatus()">获取采集状态</button>
            <div id="test3-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>测试4: 可用浏览器列表</h3>
            <button onclick="testAvailableBrowsers()">获取可用浏览器</button>
            <div id="test4-result" class="result"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://127.0.0.1:8000/api/v1/bitbrowser';

        async function makeRequest(url, options = {}) {
            try {
                const response = await fetch(url, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                return await response.json();
            } catch (error) {
                throw error;
            }
        }

        function showResult(elementId, data, isError = false) {
            const element = document.getElementById(elementId);
            element.className = `result ${isError ? 'error' : 'success'}`;
            element.textContent = isError ? `❌ 错误: ${data}` : `✅ 成功!\n${JSON.stringify(data, null, 2)}`;
        }

        async function testConnection() {
            try {
                const data = await makeRequest(`${API_BASE}/test`);
                showResult('test1-result', data);
            } catch (error) {
                showResult('test1-result', error.message, true);
            }
        }

        async function testSystemStatus() {
            try {
                const data = await makeRequest(`${API_BASE}/system/status`);
                showResult('test2-result', data);
            } catch (error) {
                showResult('test2-result', error.message, true);
            }
        }

        async function testCollectionStatus() {
            try {
                const data = await makeRequest(`${API_BASE}/collection/status`);
                showResult('test3-result', data);
            } catch (error) {
                showResult('test3-result', error.message, true);
            }
        }

        async function testAvailableBrowsers() {
            try {
                const data = await makeRequest(`${API_BASE}/browsers/available`);
                showResult('test4-result', data);
            } catch (error) {
                showResult('test4-result', error.message, true);
            }
        }

        // 页面加载时自动测试连接
        window.onload = function() {
            console.log('页面加载完成，开始自动测试...');
            testConnection();
        };
    </script>
</body>
</html>
