#!/usr/bin/env python3
"""
简单的测试服务器 - 用于验证前端连接
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

app = FastAPI(title="简单测试服务器")

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {"message": "简单测试服务器运行正常"}

@app.get("/api/v1/bitbrowser/test")
async def test_connection():
    return {
        "success": True,
        "data": {
            "status": "ok",
            "message": "API连接正常",
            "timestamp": "2025-08-05T19:00:00Z"
        },
        "message": "API连接正常",
        "timestamp": "2025-08-05T19:00:00Z"
    }

@app.get("/api/v1/bitbrowser/system/status")
async def get_system_status():
    data = {
        "bitbrowser_connected": True,
        "total_browsers": 2,
        "available_browsers": 2,
        "system_ready": True,
        "network_status": {
            "total": 2,
            "normal": 2,
            "no_proxy": 0,
            "skip": 0,
            "error": 0,
            "unknown": 0
        },
        "browsers": [
            {
                "id": "test_browser_1",
                "name": "测试浏览器1",
                "seq": 1,
                "remark": "测试浏览器1",
                "is_open": False,
                "pid": None,
                "network_status": "normal",
                "proxy_status": {"success": True, "status": "normal", "message": "正常"}
            },
            {
                "id": "test_browser_2",
                "name": "测试浏览器2",
                "seq": 2,
                "remark": "测试浏览器2",
                "is_open": False,
                "pid": None,
                "network_status": "normal",
                "proxy_status": {"success": True, "status": "normal", "message": "正常"}
            }
        ]
    }
    return {
        "success": True,
        "data": data,
        "message": "系统状态获取成功",
        "timestamp": "2025-08-05T19:00:00Z"
    }

@app.get("/api/v1/bitbrowser/collection/status")
async def get_collection_status():
    data = {
        "is_running": False,
        "current_task": None,
        "completed_tasks": 0,
        "failed_tasks": 0,
        "active_collections": 0,
        "results": []
    }
    return {
        "success": True,
        "data": data,
        "message": "采集状态获取成功",
        "timestamp": "2025-08-05T19:00:00Z"
    }

@app.post("/api/v1/bitbrowser/collect/single/{browser_id}")
async def collect_single_browser(browser_id: str):
    data = {
        "success": True,
        "browser_id": browser_id,
        "browser_name": f"测试浏览器_{browser_id}",
        "notes_count": 10,
        "data": {
            "total_count": 10,
            "collected_at": "2025-08-05T19:00:00Z",
            "page_url": "https://www.xiaohongshu.com/explore",
            "notes": []
        }
    }
    return {
        "success": True,
        "data": data,
        "message": "单个浏览器采集成功",
        "timestamp": "2025-08-05T19:00:00Z"
    }

@app.post("/api/v1/bitbrowser/collect/multiple")
async def collect_multiple_browsers(request: dict):
    browser_ids = request.get("browser_ids", [])
    results = []
    for browser_id in browser_ids:
        results.append({
            "success": True,
            "browser_id": browser_id,
            "browser_name": f"测试浏览器_{browser_id}",
            "notes_count": 10,
            "data": {
                "total_count": 10,
                "collected_at": "2025-08-05T19:00:00Z",
                "page_url": "https://www.xiaohongshu.com/explore",
                "notes": []
            }
        })
    return {
        "success": True,
        "data": results,
        "message": "批量采集成功",
        "timestamp": "2025-08-05T19:00:00Z"
    }

@app.delete("/api/v1/bitbrowser/collect/cancel/{browser_id}")
async def cancel_collection(browser_id: str):
    data = {"message": f"已取消浏览器 {browser_id} 的采集任务"}
    return {
        "success": True,
        "data": data,
        "message": f"已取消浏览器 {browser_id} 的采集任务",
        "timestamp": "2025-08-05T19:00:00Z"
    }

@app.get("/api/v1/bitbrowser/browsers/available")
async def get_available_browsers():
    data = {
        "total_available": 2,
        "browsers": [
            {
                "id": "test_browser_1",
                "name": "测试浏览器1",
                "seq": 1,
                "remark": "测试浏览器1",
                "is_open": False,
                "pid": None,
                "network_status": "normal",
                "proxy_status": {"success": True, "status": "normal", "message": "正常"}
            },
            {
                "id": "test_browser_2",
                "name": "测试浏览器2",
                "seq": 2,
                "remark": "测试浏览器2",
                "is_open": False,
                "pid": None,
                "network_status": "normal",
                "proxy_status": {"success": True, "status": "normal", "message": "正常"}
            }
        ]
    }
    return {
        "success": True,
        "data": data,
        "message": "可用浏览器列表获取成功",
        "timestamp": "2025-08-05T19:00:00Z"
    }

@app.get("/api/v1/bitbrowser/scan/windows")
async def scan_windows():
    data = {
        "success": True,
        "total_found": 2,
        "windows": [
            {
                "id": "test_browser_1",
                "name": "测试浏览器1",
                "seq": 1,
                "remark": "测试浏览器1",
                "is_open": False,
                "pid": None,
                "network_status": "normal"
            },
            {
                "id": "test_browser_2",
                "name": "测试浏览器2",
                "seq": 2,
                "remark": "测试浏览器2",
                "is_open": False,
                "pid": None,
                "network_status": "normal"
            }
        ]
    }
    return {
        "success": True,
        "data": data,
        "message": "窗口扫描成功",
        "timestamp": "2025-08-05T19:00:00Z"
    }

if __name__ == "__main__":
    print("🚀 启动简单测试服务器...")
    print("📍 服务地址: http://127.0.0.1:8000")
    print("🧪 测试端点: http://127.0.0.1:8000/api/v1/bitbrowser/test")
    print("📊 系统状态: http://127.0.0.1:8000/api/v1/bitbrowser/system/status")

    uvicorn.run(app, host="127.0.0.1", port=8000)
