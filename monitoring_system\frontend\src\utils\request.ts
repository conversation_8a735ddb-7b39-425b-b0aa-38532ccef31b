import axios from 'axios'
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useUserStore } from '@/stores/user'
import { useAppStore } from '@/stores/app'
import { getToken } from '@/utils/auth'
import router from '@/router'

// 响应数据接口
export interface ApiResponse<T = any> {
  success: boolean
  data: T
  message: string
  timestamp: string
  request_id?: string
}

// 错误响应接口
export interface ApiError {
  success: false
  error: {
    code: string
    message: string
    details?: any
  }
  timestamp: string
  request_id?: string
}

// API基础URL - 强制使用正确的地址
const API_BASE_URL = 'http://127.0.0.1:8000/api/v1'
console.log('🔧 API Base URL:', API_BASE_URL)
console.log('🔧 环境变量 VITE_API_BASE_URL:', import.meta.env.VITE_API_BASE_URL)

// 创建axios实例
const service: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 60000, // 增加到60秒
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
service.interceptors.request.use(
  (config: AxiosRequestConfig) => {
    const appStore = useAppStore()
    
    // 显示加载状态
    appStore.setLoading(true)
    
    // 添加认证token
    const token = getToken()
    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`
    }
    
    // 添加请求ID
    if (config.headers) {
      config.headers['X-Request-ID'] = generateRequestId()
    }
    
    return config
  },
  (error) => {
    const appStore = useAppStore()
    appStore.setLoading(false)
    
    console.error('请求拦截器错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    const appStore = useAppStore()
    appStore.setLoading(false)
    
    const { data } = response
    
    // 检查业务状态码
    if (data.success) {
      return data
    } else {
      // 业务错误
      const errorMessage = data.message || '请求失败'
      ElMessage.error(errorMessage)
      return Promise.reject(new Error(errorMessage))
    }
  },
  async (error) => {
    const appStore = useAppStore()
    const userStore = useUserStore()
    appStore.setLoading(false)
    
    const { response } = error
    
    if (response) {
      const { status, data } = response
      
      switch (status) {
        case 401:
          // 未授权 - token过期或无效
          await handleUnauthorized(data)
          break
          
        case 403:
          // 权限不足
          ElMessage.error('权限不足')
          router.push('/403')
          break
          
        case 404:
          // 资源不存在
          ElMessage.error('请求的资源不存在')
          break
          
        case 422:
          // 参数验证错误
          handleValidationError(data)
          break
          
        case 429:
          // 请求过于频繁
          ElMessage.error('请求过于频繁，请稍后再试')
          break
          
        case 500:
          // 服务器内部错误
          ElMessage.error('服务器内部错误，请稍后重试')
          break
          
        case 502:
        case 503:
        case 504:
          // 服务不可用
          ElMessage.error('服务暂时不可用，请稍后重试')
          break
          
        default:
          // 其他错误
          const errorMessage = data?.error?.message || data?.message || `请求失败 (${status})`
          ElMessage.error(errorMessage)
      }
    } else if (error.code === 'ECONNABORTED') {
      // 请求超时
      ElMessage.error('请求超时，请检查网络连接')
    } else if (error.message === 'Network Error') {
      // 网络错误
      ElMessage.error('网络连接失败，请检查网络设置')
    } else {
      // 其他错误
      ElMessage.error(error.message || '未知错误')
    }
    
    return Promise.reject(error)
  }
)

// 处理未授权错误
async function handleUnauthorized(data: any) {
  const userStore = useUserStore()
  
  // 尝试刷新token
  if (userStore.isLoggedIn) {
    try {
      await userStore.refreshToken()
      // 刷新成功，重新发起原请求
      return
    } catch (refreshError) {
      // 刷新失败，需要重新登录
      console.error('Token刷新失败:', refreshError)
    }
  }
  
  // 清除用户状态并跳转到登录页
  await userStore.logout()
  
  ElMessageBox.confirm(
    '登录状态已过期，请重新登录',
    '提示',
    {
      confirmButtonText: '重新登录',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    router.push('/login')
  }).catch(() => {
    // 用户取消
  })
}

// 处理参数验证错误
function handleValidationError(data: any) {
  if (data?.error?.details && Array.isArray(data.error.details)) {
    // 显示详细的验证错误
    const errors = data.error.details.map((item: any) => 
      `${item.field}: ${item.message}`
    ).join('\n')
    
    ElMessage.error({
      message: `参数验证失败:\n${errors}`,
      duration: 5000
    })
  } else {
    ElMessage.error(data?.error?.message || '参数验证失败')
  }
}

// 生成请求ID
function generateRequestId(): string {
  return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
}

// 请求方法封装
export const request = {
  get<T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    return service.get(url, config)
  },
  
  post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    return service.post(url, data, config)
  },
  
  put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    return service.put(url, data, config)
  },
  
  delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    return service.delete(url, config)
  },
  
  patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    return service.patch(url, data, config)
  }
}

export default service
